#!/usr/bin/env python3
"""
Corrected evaluation script that matches the training pipeline exactly.
Fixes all 4 major discrepancies identified in the investigation.
"""

import os
import sys
import math
import argparse
import yaml
from typing import List, Dict, Tuple
from datetime import datetime

import torch
import torch.nn.functional as F

# External deps
try:
    from beir import util
    from beir.datasets.data_loader import GenericDataLoader
    from beir.retrieval.evaluation import EvaluateRetrieval as Evaluate
except Exception as e:
    print(f"BEIR import failed: {e}. Try: pip install beir")
    sys.exit(1)

# Add project root to path
sys.path.append(os.path.dirname(__file__))
from models import MaskedLanguageModel
from models.config import load_model_config_from_yaml
from MyTokenizer import AutoCLSTokenizer, is_qwen_tokenizer


def build_tokenizer_correctly(tokenizer_path: str):
    """Build tokenizer using the SAME method as training script."""
    print(f"[Info] Loading tokenizer from: {tokenizer_path}")

    # Use EXACT same logic as train.py lines 111-120
    if is_qwen_tokenizer(tokenizer_path):
        tokenizer = AutoCLSTokenizer(tokenizer_path)
    else:
        from transformers import AutoTokenizer
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)  # This is what training uses

    # Apply same fallback logic as training
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    if tokenizer.cls_token is None:
        tokenizer.add_special_tokens({'cls_token': '[CLS]'})
    if tokenizer.mask_token is None:
        tokenizer.add_special_tokens({'mask_token': '[MASK]'})

    print(f"[Info] Tokenizer vocab size: {len(tokenizer)}")
    print(f"[Info] Special tokens - PAD: {tokenizer.pad_token_id}, CLS: {tokenizer.cls_token_id}, EOS: {tokenizer.eos_token_id}, MASK: {tokenizer.mask_token_id}")
    print(f"[Info] Using AutoCLSTokenizer: {isinstance(tokenizer, AutoCLSTokenizer)}")

    return tokenizer


def create_sequence_correctly(tokenizer, text: str, model, max_len: int, device: torch.device):
    """Create sequence using EXACT same logic as DataLoaderStreaming._create_sequence_tokens()"""

    # Step 1: Tokenize text (standard tokenizer, NO automatic CLS addition)
    # Training uses standard AutoTokenizer, not AutoCLSTokenizer
    encoded = tokenizer.encode(text, add_special_tokens=False, truncation=True, max_length=max_len-17)  # Reserve space for 16 CLS + 1 EOS
    content_tokens = encoded  # No CLS token to remove

    # Step 2: Apply EXACT same logic as DataLoaderStreaming._create_sequence_tokens()
    if model.use_multi_cls:
        # Multi-CLS approach: reserve space for n_cls_tokens + EOS
        content_length = max_len - model.n_cls_tokens - 1
        content_tokens = content_tokens[:content_length]

        # FIX 2: Use model's actual CLS token IDs (60000-60015), not artificial ones
        sequence = model.cls_token_ids[:model.n_cls_tokens] + content_tokens + [tokenizer.eos_token_id]
    else:
        # Standard approach: reserve space for CLS + EOS
        content_length = max_len - 2
        content_tokens = content_tokens[:content_length]
        sequence = [tokenizer.cls_token_id] + content_tokens + [tokenizer.eos_token_id]

    # Pad to full length
    while len(sequence) < max_len:
        sequence.append(tokenizer.pad_token_id)

    return torch.tensor(sequence[:max_len], dtype=torch.long, device=device)


def get_embeddings_correctly(model: MaskedLanguageModel, input_ids: torch.Tensor, attention_mask: torch.Tensor, normalize: bool = True, pooling: str = "cls_avg"):
    """Get embeddings using EXACT same logic as model forward pass for embedding lookup."""
    
    B, T = input_ids.size()
    
    # FIX 3: Use EXACT same embedding routing as model forward pass (lines 189-218)
    if model.use_multi_cls:
        # Multi-CLS approach: handle regular tokens and CLS tokens separately
        x = torch.zeros(B, T, model.config.n_embd, device=input_ids.device, dtype=model.transformer.wte.weight.dtype)
        
        # Regular tokens (including MASK, PAD, EOS but not CLS tokens)
        regular_mask = torch.ones_like(input_ids, dtype=torch.bool)
        for cls_id in model.cls_token_ids:
            regular_mask = regular_mask & (input_ids != cls_id)
        
        if regular_mask.any():
            # Ensure token IDs are within vocabulary range
            regular_tokens = input_ids[regular_mask]
            vocab_size = model.transformer.wte.num_embeddings
            
            # Clamp token IDs to valid range
            if (regular_tokens >= vocab_size).any():
                print(f"Warning: Found token IDs >= vocab_size ({vocab_size}): {regular_tokens[regular_tokens >= vocab_size].unique()}")
                regular_tokens = torch.clamp(regular_tokens, 0, vocab_size - 1)
            
            x[regular_mask] = model.transformer.wte(regular_tokens)
        
        # CLS tokens use separate embeddings
        for i, cls_id in enumerate(model.cls_token_ids):
            cls_mask = (input_ids == cls_id)
            if cls_mask.any():
                x[cls_mask] = model.cls_embeddings[i]
    else:
        # Standard approach: use single embedding layer
        x = model.transformer.wte(input_ids)
    
    # Forward through transformer layers
    for block in model.transformer.h:
        x = block(x, attention_mask=attention_mask)
    x = model.transformer.ln_f(x)
    
    # Apply pooling strategy
    if pooling == "cls_avg" and model.use_multi_cls:
        # Average of all 16 CLS tokens
        cls_tokens = x[:, :model.n_cls_tokens, :]  # (B, 16, C)
        pooled = cls_tokens.mean(dim=1)  # (B, C)
    elif pooling == "cls_weighted" and model.use_multi_cls:
        # Weighted average using cls_weight_proj
        cls_tokens = x[:, :model.n_cls_tokens, :]  # (B, 16, C)
        raw_weights = model.cls_weight_proj(cls_tokens).squeeze(-1)  # (B, 16)
        weights = F.softmax(raw_weights, dim=-1)  # (B, 16)
        pooled = torch.einsum('bi,bic->bc', weights, cls_tokens)  # (B, C)
    elif pooling == "cls_concat" and model.use_multi_cls:
        # Concatenate all 16 CLS tokens
        cls_tokens = x[:, :model.n_cls_tokens, :]  # (B, 16, C)
        if normalize:
            cls_tokens = F.normalize(cls_tokens, p=2, dim=-1)
        pooled = cls_tokens.view(cls_tokens.size(0), -1)  # (B, 16*C)
    elif pooling == "first_last_cls_avg" and model.use_multi_cls:
        # Average of first and last CLS tokens
        cls_tokens = x[:, :model.n_cls_tokens, :]  # (B, 16, C)
        first_cls = cls_tokens[:, 0, :]  # (B, C)
        last_cls = cls_tokens[:, -1, :]  # (B, C)
        pooled = (first_cls + last_cls) / 2.0  # (B, C)
    elif pooling == "mean":
        # Mean pooling over non-pad tokens
        mask = attention_mask.to(x.dtype)  # (B, T)
        denom = mask.sum(dim=1, keepdim=True).clamp_min(1.0)  # (B,1)
        pooled = (x * mask.unsqueeze(-1)).sum(dim=1) / denom
    else:
        # Default: use first token (CLS)
        pooled = x[:, 0, :]
    
    if normalize and pooling not in ["cls_concat"]:
        pooled = F.normalize(pooled, p=2, dim=-1)
    
    return pooled


def batch_encode_correctly(tokenizer, texts: List[str], model, max_len: int, device: torch.device):
    """Encode batch of texts using correct sequence creation logic."""
    input_ids = []
    for text in texts:
        sequence = create_sequence_correctly(tokenizer, text, model, max_len, device)
        input_ids.append(sequence)
    
    input_ids = torch.stack(input_ids)
    attention_mask = (input_ids != tokenizer.pad_token_id).long()
    
    return input_ids, attention_mask


def compute_results(queries: Dict[str, str], corpus: Dict[str, Dict[str, str]], qrels: Dict[str, Dict[str, int]],
                   model: MaskedLanguageModel, tokenizer, batch_size: int, max_len: int, device: torch.device, pooling: str = "cls_avg") -> Dict[str, Dict[str, float]]:
    """Compute retrieval results using corrected pipeline."""
    
    # Encode corpus
    doc_ids = list(corpus.keys())
    doc_texts = [corpus[did]["text"] if corpus[did].get("title") is None else (corpus[did]["title"] + " " + corpus[did]["text"]) for did in doc_ids]
    
    all_doc_embs = []
    model.eval()
    with torch.no_grad():
        for i in range(0, len(doc_texts), batch_size):
            batch_texts = doc_texts[i:i+batch_size]
            input_ids, attn = batch_encode_correctly(tokenizer, batch_texts, model, max_len, device)
            embs = get_embeddings_correctly(model, input_ids, attn, normalize=True, pooling=pooling)
            all_doc_embs.append(embs)
    
    doc_matrix = torch.cat(all_doc_embs, dim=0)  # [N_docs, dim]
    
    # Encode queries and compute scores
    results: Dict[str, Dict[str, float]] = {}
    with torch.no_grad():
        for qid, qtext in queries.items():
            q_input, q_attn = batch_encode_correctly(tokenizer, [qtext], model, max_len, device)
            q_emb = get_embeddings_correctly(model, q_input, q_attn, normalize=True, pooling=pooling)  # [1, dim]
            
            # Cosine similarity via dot product of normalized vectors
            scores = torch.matmul(q_emb, doc_matrix.T).squeeze(0)  # [N_docs]
            
            # Convert to dict of top scores
            topk = min(1000, scores.size(0))
            vals, idxs = torch.topk(scores, k=topk, largest=True)
            res = {doc_ids[j.item()]: float(vals[k].item()) for k, j in enumerate(idxs)}
            results[qid] = res
    
    return results


def main():
    parser = argparse.ArgumentParser(description="Corrected evaluation script matching training pipeline")
    parser.add_argument("--checkpoint", type=str, required=True, help="Path to model checkpoint")
    parser.add_argument("--config", type=str, default="config_multi_cls.yaml", help="Path to model config")
    parser.add_argument("--tokenizer_path", type=str, default="/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls", help="Path to tokenizer")
    parser.add_argument("--dataset", type=str, default="scifact", help="BEIR dataset name")
    parser.add_argument("--data_dir", type=str, default="./beir_datasets", help="Directory for BEIR datasets")
    parser.add_argument("--batch_size", type=int, default=32)
    parser.add_argument("--max_len", type=int, default=1024)
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu")
    parser.add_argument("--pooling", type=str, choices=["cls_avg", "cls_weighted", "cls_concat", "first_last_cls_avg", "mean"], default="cls_avg")
    args = parser.parse_args()
    
    device = torch.device(args.device)
    
    # FIX 1: Use correct tokenizer initialization (same as training)
    tokenizer = build_tokenizer_correctly(args.tokenizer_path)
    
    # Load model config and create model with tokenizer (same as training)
    model_config = load_model_config_from_yaml(args.config)
    model = MaskedLanguageModel(model_config, tokenizer=tokenizer).to(device)  # FIX: Pass tokenizer
    
    # Load checkpoint
    print(f"[Info] Loading checkpoint: {args.checkpoint}")
    ckpt = torch.load(args.checkpoint, map_location=device, weights_only=False)
    state_dict = ckpt.get("model", ckpt)
    
    # Handle compiled models
    if any(key.startswith("_orig_mod.") for key in state_dict.keys()):
        print("[Info] Detected compiled model, removing _orig_mod. prefix")
        state_dict = {key.replace("_orig_mod.", ""): value for key, value in state_dict.items()}
    
    missing, unexpected = model.load_state_dict(state_dict, strict=False)
    if missing:
        print(f"[Warn] Missing keys: {len(missing)}")
    if unexpected:
        print(f"[Warn] Unexpected keys: {len(unexpected)}")
    
    model.eval()
    
    # Verify model setup
    print(f"\n[Verification] Model multi-CLS setup:")
    print(f"  use_multi_cls: {model.use_multi_cls}")
    print(f"  n_cls_tokens: {model.n_cls_tokens}")
    print(f"  cls_token_ids: {model.cls_token_ids[:3]}...{model.cls_token_ids[-3:] if len(model.cls_token_ids) > 6 else model.cls_token_ids}")
    print(f"  cls_embeddings shape: {model.cls_embeddings.shape if hasattr(model, 'cls_embeddings') else 'N/A'}")
    
    # Download and load dataset
    os.makedirs(args.data_dir, exist_ok=True)
    dataset_url = util.download_and_unzip(f"https://public.ukp.informatik.tu-darmstadt.de/thakur/BEIR/datasets/{args.dataset}.zip", args.data_dir)
    data_folder = os.path.join(args.data_dir, args.dataset)
    corpus, queries, qrels = GenericDataLoader(data_folder).load(split="test")
    
    print(f"\n[Dataset] {args.dataset}: {len(queries)} queries, {len(corpus)} documents")
    
    # Test sequence creation with first query
    first_query = list(queries.values())[0]
    print(f"\n[Debug] Testing sequence creation:")
    print(f"  Query: '{first_query[:50]}...'")
    test_seq = create_sequence_correctly(tokenizer, first_query, model, args.max_len, device)
    print(f"  Sequence shape: {test_seq.shape}")
    print(f"  First 20 tokens: {test_seq[:20].tolist()}")
    if model.use_multi_cls:
        expected_cls = model.cls_token_ids[:model.n_cls_tokens]
        actual_cls = test_seq[:model.n_cls_tokens].tolist()
        print(f"  Expected CLS tokens: {expected_cls}")
        print(f"  Actual CLS tokens: {actual_cls}")
        print(f"  CLS tokens match: {actual_cls == expected_cls}")
    
    # Run evaluation
    print(f"\n[Evaluation] Running with pooling: {args.pooling}")
    results = compute_results(queries, corpus, qrels, model, tokenizer, args.batch_size, args.max_len, device, pooling=args.pooling)
    
    # Compute metrics
    def compute_metrics(qrels, results, k_values):
        metrics = {k: {'ndcg': [], 'recall': [], 'precision': []} for k in k_values}
        
        for qid in qrels:
            if qid not in results:
                continue
            
            ranked_docs = list(results[qid].keys())
            relevant_docs = set(qrels[qid].keys())
            
            for k in k_values:
                top_k = ranked_docs[:k]
                relevant_in_k = [doc for doc in top_k if doc in relevant_docs]
                
                # Recall@k
                recall_k = len(relevant_in_k) / len(relevant_docs) if relevant_docs else 0.0
                metrics[k]['recall'].append(recall_k)
                
                # Precision@k
                precision_k = len(relevant_in_k) / k if k > 0 else 0.0
                metrics[k]['precision'].append(precision_k)
                
                # NDCG@k
                dcg = sum(1.0 / math.log2(i + 2) for i, doc in enumerate(top_k) if doc in relevant_docs)
                idcg = sum(1.0 / math.log2(i + 2) for i in range(min(k, len(relevant_docs))))
                ndcg_k = dcg / idcg if idcg > 0 else 0.0
                metrics[k]['ndcg'].append(ndcg_k)
        
        # Average across queries
        avg_metrics = {}
        for k in k_values:
            avg_metrics[k] = {
                'ndcg': sum(metrics[k]['ndcg']) / len(metrics[k]['ndcg']) if metrics[k]['ndcg'] else 0.0,
                'recall': sum(metrics[k]['recall']) / len(metrics[k]['recall']) if metrics[k]['recall'] else 0.0,
                'precision': sum(metrics[k]['precision']) / len(metrics[k]['precision']) if metrics[k]['precision'] else 0.0
            }
        return avg_metrics
    
    k_values = [1, 3, 5, 10, 100]
    metrics = compute_metrics(qrels, results, k_values)
    
    print(f"\n[Results] Corrected evaluation metrics:")
    print(f"NDCG@10: {metrics[10]['ndcg']:.4f}")
    for k in k_values:
        nd = metrics[k]['ndcg']
        rc = metrics[k]['recall']
        pr = metrics[k]['precision']
        print(f"k={k}: NDCG={nd:.4f}, Recall={rc:.4f}, P@k={pr:.4f}")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"eval/corrected_results_{args.dataset}_{args.pooling}_{timestamp}.md"
    os.makedirs("eval", exist_ok=True)
    
    with open(results_file, 'w') as f:
        f.write(f"# Corrected Evaluation Results\n\n")
        f.write(f"**Dataset:** {args.dataset}\n")
        f.write(f"**Pooling:** {args.pooling}\n")
        f.write(f"**Checkpoint:** {os.path.basename(args.checkpoint)}\n")
        f.write(f"**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"**Primary Metric (NDCG@10):** {metrics[10]['ndcg']:.4f}\n\n")
        f.write("## Detailed Metrics\n\n")
        f.write("| k | NDCG | Recall | Precision |\n")
        f.write("|---|------|--------|-----------|\n")
        for k in k_values:
            f.write(f"| {k} | {metrics[k]['ndcg']:.4f} | {metrics[k]['recall']:.4f} | {metrics[k]['precision']:.4f} |\n")
    
    print(f"\n[Saved] Results saved to: {results_file}")


if __name__ == "__main__":
    main()
