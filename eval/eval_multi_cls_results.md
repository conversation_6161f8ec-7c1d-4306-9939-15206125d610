[Info] Loading checkpoint from: /home/<USER>/notebooks/Ali_embedding/Ali_embedding_multcls/log_encoder_mlm_fineweb_10B_pure_multi_cls/model_19999.pt                                                                                                                       
[Info] Detected compiled model checkpoint, removing _orig_mod. prefix                                                                                                                                                                                                          
[Info] Checkpoint vocabulary size: 50368                                                                                                                                                                                                                                       
[Debug] Multi-CLS model: adjusted base vocab_size to: 50349                                                                                                                                                                                                                    
[Debug] Config use_multi_cls: True                                                                                                                                                                                                                                             
[Debug] Model transformer.wte.weight.shape: torch.Size([50368, 768])                                                                                                                                                                                                           
                                                                                                                                                                                                                                                                               
[Debug] Checking CLS token embeddings...                                                                                                                                                                                                                                       
[Debug] CLS embeddings shape: torch.Size([16, 768])                                                                                                                                                                                                                            
[Debug] Regular vocab embedding shape: torch.Size([50368, 768])                                                                                                                                                                                                                
[Debug] All 16 CLS embeddings identical: False                                                                                                                                                                                                                                 
[Debug] ||CLS1 - CLS2|| = 0.781920                                                                                                                                                                                                                                             
[Debug] ||CLS1 - CLS16|| = 0.813740                                                                                                                                                                                                                                            
                                                                                                                                                                                                                                                                               
[Info] Loading tokenizer from: /s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls/                                                                                                                                                                                            
[Info] Tokenizer vocab size: 50386 (base: 50349)                                                                                                                                                                                                                               
[Info] Special tokens - MASK: 50368, PAD: 50369                                                                                                                                                                                                                                
[Info] CLS tokens: [50370, 50371, 50372]...[50383, 50384, 50385] (showing first 3 and last 3)                                                                                                                                                                                  
[Warn] Tokenizer vocab (50386) > model vocab (50367). Will clamp token IDs.                                                                                                                                                                                                    
[Debug] Testing first query to check CLS hidden states...                                                                                                                                                                                                                      
[Debug] First query: '0-dimensional biomaterials show inductive properti...'                                                                                                                                                                                                   
[Debug] Input token IDs (first 20): [50370, 50371, 50372, 50373, 50374, 50375, 50376, 50377, 50378, 50379, 50380, 50381, 50382, 50383, 50384, 50385, 263, 275, 282, 10840]                                                                                                     
[Debug] Expected CLS tokens: [50370, 50371, 50372, 50373, 50374, 50375, 50376, 50377, 50378, 50379, 50380, 50381, 50382, 50383, 50384, 50385]                                                                                                                                  
[Debug] Are first 16 tokens the CLS tokens? True                                                                                                                                                                                                                               
                                                                                                                                                                                                                                                                               
Testing cls_avg with debug info...                                                                                                                                                                                                                                             
[Debug] After embedding - ||CLS1 - CLS2||: 0.000000                                                                                                                                                                                                                            
[Debug] After transformer - ||CLS1 - CLS2||: 0.017134                                                                                                                                                                                                                          
[Debug] All 16 CLS hidden states identical after transformer: False                                                                                                                                                                                                            
[Debug] Average std deviation across CLS token dimensions: 0.003191                                                                                                                                                                                                            
[Debug] First CLS norm: 36.621307, Average CLS norm: 36.621113                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                               
============================================================                                                                                                                                                                                                                   
Evaluating pooling method: cls_avg                                                                                                                                                                                                                                             
============================================================                                                                                                                                                                                                                   
Queries with hits@10: 108/300 (36.0%)                                                                                                                                                                                                                                          
Queries with hits@100: 190/300 (63.3%)                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                                               
Metrics for cls_avg:                                                                                                                                                                                                                                                           
NDCG@10: 0.2315                                                                                                                                                                                                                                                                
k=1: NDCG=0.1433, Recall=0.1372, P@k=0.1433                                                                                                                                                                                                                                    
k=3: NDCG=0.1857, Recall=0.2142, P@k=0.0789                                                                                                                                                                                                                                    
k=5: NDCG=0.2052, Recall=0.2611, P@k=0.0587                                                                                                                                                                                                                                    
k=10: NDCG=0.2315, Recall=0.3411, P@k=0.0380                                                                                                                                                                                                                                   
k=100: NDCG=0.2871, Recall=0.6148, P@k=0.0069                                                                                                                                                                                                                                  
                                                                                                                                                                                                                                                                               
Testing cls_weighted with debug info...                                                                                                                                                                                                                                        
[Debug] After embedding - ||CLS1 - CLS2||: 0.000000                                                                                                                                                                                                                            
[Debug] After transformer - ||CLS1 - CLS2||: 0.017134
[Debug] All 16 CLS hidden states identical after transformer: False 
[Debug] Weight distribution - std: 0.000459, max: 0.062902, min: 0.060841
[Debug] Top 3 weights: [0.06290168315172195, 0.06275614351034164, 0.06274249404668808]
[Debug] Average CLS norm: 36.621113, Weighted CLS norm: 36.621113

============================================================
Evaluating pooling method: cls_weighted
============================================================
Queries with hits@10: 108/300 (36.0%)
Queries with hits@100: 191/300 (63.7%)

Metrics for cls_weighted:
NDCG@10: 0.2314
k=1: NDCG=0.1433, Recall=0.1372, P@k=0.1433
k=3: NDCG=0.1854, Recall=0.2142, P@k=0.0789
k=5: NDCG=0.2050, Recall=0.2611, P@k=0.0587
k=10: NDCG=0.2314, Recall=0.3411, P@k=0.0380
k=100: NDCG=0.2877, Recall=0.6181, P@k=0.0069

Testing cls_concat with debug info...
[Debug] After embedding - ||CLS1 - CLS2||: 0.000000
[Debug] After transformer - ||CLS1 - CLS2||: 0.017134
[Debug] All 16 CLS hidden states identical after transformer: False 

============================================================
Evaluating pooling method: cls_concat
============================================================
Queries with hits@10: 108/300 (36.0%)
Queries with hits@100: 190/300 (63.3%)

Metrics for cls_concat:
NDCG@10: 0.2316
k=1: NDCG=0.1433, Recall=0.1372, P@k=0.1433
k=3: NDCG=0.1857, Recall=0.2142, P@k=0.0789
k=5: NDCG=0.2052, Recall=0.2611, P@k=0.0587
k=10: NDCG=0.2316, Recall=0.3411, P@k=0.0380
k=100: NDCG=0.2871, Recall=0.6148, P@k=0.0069

Testing cls_concat_pre_proj with debug info...
[Debug] After embedding - ||CLS1 - CLS2||: 0.000000
[Debug] Pre-proj CLS std: 0.000088, Concat norm: 4.000000

============================================================
Evaluating pooling method: cls_concat_pre_proj
============================================================
Queries with hits@10: 107/300 (35.7%)
Queries with hits@100: 193/300 (64.3%)

Metrics for cls_concat_pre_proj:
NDCG@10: 0.2316
k=1: NDCG=0.1467, Recall=0.1389, P@k=0.1467
k=3: NDCG=0.1865, Recall=0.2142, P@k=0.0789
k=5: NDCG=0.2074, Recall=0.2644, P@k=0.0593
k=10: NDCG=0.2316, Recall=0.3378, P@k=0.0377
k=100: NDCG=0.2898, Recall=0.6248, P@k=0.0070

Testing first_last_cls_avg with debug info...
[Debug] After embedding - ||CLS1 - CLS2||: 0.000000
[Debug] After transformer - ||CLS1 - CLS2||: 0.017134
[Debug] All 16 CLS hidden states identical after transformer: False 
[Debug] First CLS norm: 36.621307, Last CLS norm: 36.622540
[Debug] First-Last CLS difference norm: 0.362548
[Debug] Average First-Last CLS norm: 36.621475

============================================================
Evaluating pooling method: first_last_cls_avg
============================================================
Queries with hits@10: 108/300 (36.0%)
Queries with hits@100: 186/300 (62.0%)

Metrics for first_last_cls_avg:
NDCG@10: 0.2253
k=1: NDCG=0.1333, Recall=0.1256, P@k=0.1333
k=3: NDCG=0.1794, Recall=0.2108, P@k=0.0778
k=5: NDCG=0.1979, Recall=0.2561, P@k=0.0573
k=10: NDCG=0.2253, Recall=0.3400, P@k=0.0377
k=100: NDCG=0.2789, Recall=0.6014, P@k=0.0067

Testing two-stage pooling with top-20 reranking...
============================================================
Evaluating two-stage pooling: cls_avg -> multi-vector rerank (top-20)
============================================================
[Two-Stage] Stage 1: Initial retrieval with cls_avg, retrieving top-20 candidates
[Two-Stage] Stage 2: Reranking top-20 candidates with multi-vector CLS (16 vectors)