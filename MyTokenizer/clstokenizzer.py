from typing import Union, List
from transformers import AutoTokenizer
from pathlib import Path
import json
import os
import torch

class AutoCLSTokenizer:
    """Custom AutoTokenizer wrapper that automatically adds CLS tokens to every sentence."""
    
    def __init__(self, tokenizer_path: str):
        self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        # Use the tokenizer's actual CLS token, not hardcoded "[cls]"
        self._cls_token = self.tokenizer.cls_token  # This will be "<s>" for this tokenizer
        self._cls_token_id = self.tokenizer.cls_token_id
        
        # Store original methods
        self._original_encode = self.tokenizer.encode
        self._original_call = self.tokenizer.__call__
        
        # Override tokenizer methods
        self.tokenizer.encode = self._encode_with_cls
        self.tokenizer.__call__ = self._call_with_cls
    
    def _ensure_cls_token(self, text: Union[str, List[str]]) -> Union[str, List[str]]:
        """Ensure CLS token is at the beginning of text(s)."""
        if isinstance(text, str):
            if not text.startswith(self._cls_token):
                return f"{self._cls_token} {text}"
            return text
        elif isinstance(text, list):
            processed_texts = []
            for t in text:
                if not t.startswith(self._cls_token):
                    processed_texts.append(f"{self._cls_token} {t}")
                else:
                    processed_texts.append(t)
            return processed_texts
        return text
    
    def _encode_with_cls(self, text: Union[str, List[str]], add_special_tokens: bool = True, **kwargs):
        """Override encode method to add CLS tokens."""
        text_with_cls = self._ensure_cls_token(text)
        return self._original_encode(text_with_cls, add_special_tokens=add_special_tokens, **kwargs)
    
    def _call_with_cls(self, text: Union[str, List[str]], add_special_tokens: bool = True, **kwargs):
        """Override __call__ method to add CLS tokens."""
        text_with_cls = self._ensure_cls_token(text)
        return self._original_call(text_with_cls, add_special_tokens=add_special_tokens, **kwargs)
    
    def encode(self, text: Union[str, List[str]], add_special_tokens: bool = True, **kwargs):
        """Encode text with automatic CLS token addition."""
        return self.tokenizer.encode(text, add_special_tokens=add_special_tokens, **kwargs)
    
    def __call__(self, text: Union[str, List[str]], add_special_tokens: bool = True, **kwargs):
        """Tokenize text with automatic CLS token addition."""
        return self.tokenizer(text, add_special_tokens=add_special_tokens, **kwargs)
    
    def decode(self, token_ids: Union[List[int], torch.Tensor], **kwargs) -> str:
        """Decode token IDs back to text."""
        return self.tokenizer.decode(token_ids, **kwargs)
    
    def batch_decode(self, token_ids: Union[List[List[int]], torch.Tensor], **kwargs) -> List[str]:
        """Batch decode token IDs back to text."""
        return self.tokenizer.batch_decode(token_ids, **kwargs)
    
    @property
    def vocab_size(self):
        return self.tokenizer.vocab_size
    
    @property
    def pad_token_id(self):
        return self.tokenizer.pad_token_id
    
    @property
    def eos_token_id(self):
        return self.tokenizer.eos_token_id
    
    @property
    def cls_token_id(self):
        return self.tokenizer.cls_token_id
    
    @property
    def mask_token_id(self):
        return self.tokenizer.mask_token_id
    
    @property
    def pad_token(self):
        return self.tokenizer.pad_token
    
    @property
    def eos_token(self):
        return self.tokenizer.eos_token
    
    @property
    def cls_token(self):
        return self.tokenizer.cls_token
    
    @property
    def mask_token(self):
        return self.tokenizer.mask_token
    
    def convert_tokens_to_ids(self, tokens):
        return self.tokenizer.convert_tokens_to_ids(tokens)
    
    def convert_ids_to_tokens(self, ids):
        return self.tokenizer.convert_ids_to_tokens(ids)
    
    def add_special_tokens(self, special_tokens_dict):
        return self.tokenizer.add_special_tokens(special_tokens_dict)

    def __len__(self):
        return len(self.tokenizer)

def is_qwen_tokenizer(tokenizer_path: str) -> bool:
    """Detect if the tokenizer is a Qwen tokenizer by checking configuration files."""
    tokenizer_dir = Path(tokenizer_path)
    
    # Check tokenizer_config.json for Qwen indicators
    config_file = tokenizer_dir / "tokenizer_config.json"
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Check tokenizer_class
            tokenizer_class = config.get("tokenizer_class", "")
            if "qwen" in tokenizer_class.lower():
                return True
                
            # Check model_max_length (Qwen often uses 131072)
            max_length = config.get("model_max_length", 0)
            if max_length == 131072:
                return True
                
        except (json.JSONDecodeError, FileNotFoundError):
            pass
    
    # Check if tokenizer directory name contains "qwen"
    if "qwen" in str(tokenizer_dir).lower():
        return True
    
    # Try loading tokenizer and check class name
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
        tokenizer_class_name = tokenizer.__class__.__name__.lower()
        if "qwen" in tokenizer_class_name:
            return True
    except Exception:
        pass
    
    return False