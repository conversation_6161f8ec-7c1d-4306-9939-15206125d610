# Investigation Results: Multi-CLS Token Handling

## Your Questions Answered

### 1. **Training Script Analysis: How are the 16 CLS tokens distributed during training?**

**Answer:** The training uses a **hybrid approach**:

- **Single CLS token input**: The `AutoCLSTokenizer` automatically prepends ONE `[cls]` token (ID: 151670) to every text sequence
- **Multi-CLS replacement**: The `DataLoaderStreaming` then replaces this single CLS token with 16 different CLS tokens at sequence positions 0-15
- **CLS token IDs**: The model generates virtual IDs `[60000, 60001, ..., 60015]` for the 16 CLS tokens
- **Sequence structure**: `[CLS1, CLS2, ..., CLS16] + content + [EOS] + [PAD]`

**Key Code Path:**
```python
# train.py line 112
tokenizer = AutoCLSTokenizer(data_config.tokenizer_path)  # Adds [cls] automatically

# dataloader_streaming.py lines 304-309
if self.use_multi_cls:
    sequence = self.cls_token_ids[:self.n_cls_tokens] + content_tokens + [self.eos_token_id]
```

### 2. **Does training use the original tokenizer's CLS token?**

**Answer:** **YES, but only as an intermediate step**:

- **Step 1**: `AutoCLSTokenizer` adds the original `[cls]` token (ID: 151670) to text
- **Step 2**: `DataLoaderStreaming` replaces this with 16 multi-CLS tokens (IDs: 60000-60015)
- **Final training input**: Contains NO original CLS token, only the 16 multi-CLS tokens

**Evidence:**
- `tokenizer_config.json` shows `"cls_token": "[cls]"` with ID 151670
- `added_tokens.json` confirms `"[cls]": 151670`
- Training data loader replaces this with virtual multi-CLS tokens

### 3. **Exact vocabulary size and token ID assignment during training?**

**Answer:**

**Tokenizer Vocabulary:**
- **Size**: 50,368 tokens (from checkpoint analysis)
- **Special tokens**: 
  - `[cls]`: 151670 (replaced during training)
  - `[MASK]`: 151669
  - `<|endoftext|>`: 151643 (PAD)
  - `<|im_end|>`: 151645 (EOS)

**Model Architecture:**
- **Regular embeddings** (`transformer.wte`): 50,368 × 768 (handles tokenizer vocab)
- **CLS embeddings** (`cls_embeddings`): 16 × 768 (separate parameters)
- **Multi-CLS token IDs**: 60000-60015 (virtual, not in tokenizer)

### 4. **Tokenizer Behavior: Does it already include mask/pad tokens?**

**Answer:** **YES, all special tokens are built-in**:

**From tokenizer files:**
- `[MASK]`: 151669 ✓ (in added_tokens.json)
- `<|endoftext|>`: 151643 ✓ (used as PAD)
- `[cls]`: 151670 ✓ (in added_tokens.json)

**Training code confirms this:**
```python
# train.py lines 115-120 - these are FALLBACKS only
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token
if tokenizer.cls_token is None:
    tokenizer.add_special_tokens({'cls_token': '[CLS]'})
```

**The tokenizer already has all required special tokens built-in.**

### 5. **Should tokenizer add additional special tokens?**

**Answer:** **NO additional tokens needed**:

- Tokenizer comes with all required special tokens pre-configured
- The `AutoCLSTokenizer` wrapper only modifies behavior (auto-prepends CLS)
- Multi-CLS tokens are handled by separate model parameters, not tokenizer vocabulary

### 6. **Training vs Evaluation Consistency?**

**Answer:** **MAJOR INCONSISTENCIES FOUND**:

| Aspect | Training | Current Evaluation | Status |
|--------|----------|-------------------|---------|
| Tokenizer | `AutoCLSTokenizer` | Raw `AutoTokenizer` | ❌ BROKEN |
| CLS token addition | Automatic | Manual/Missing | ❌ BROKEN |
| Multi-CLS IDs | 60000-60015 | 50370-50385 | ❌ BROKEN |
| Embedding lookup | `cls_embeddings` for CLS | `transformer.wte` for all | ❌ BROKEN |
| Sequence structure | `[CLS1-16] + content + EOS` | `[CLS1-16] + content` | ❌ BROKEN |

### 7. **Original Evaluation Code Analysis?**

**Answer:** **The original evaluation code has fundamental flaws**:

**Wrong Assumptions:**
1. Assumes 16 CLS tokens exist in tokenizer vocabulary
2. Uses artificial token IDs (50370-50385) instead of model's actual IDs (60000-60015)
3. Tries to use `transformer.wte` for CLS tokens instead of `cls_embeddings`

**Missing Components:**
1. No use of `AutoCLSTokenizer` (critical for training consistency)
2. No replication of data loader's multi-CLS replacement logic
3. No proper embedding routing for different token types

## Critical Discovery

**The evaluation has been testing a completely different model than what was trained.** The model learned to process sequences with:
- Automatic CLS token prepending
- 16 multi-CLS tokens with specific IDs (60000-60015)
- Separate embedding parameters for CLS tokens

But evaluation has been feeding it:
- No automatic CLS prepending
- Wrong CLS token IDs (50370-50385)
- Wrong embedding lookups

This explains why all pooling methods show similar poor performance (~0.23 NDCG@10) - the model is seeing completely unfamiliar input patterns.

## Solution

**Use the exact same tokenization and sequence processing pipeline as training:**
1. Use `AutoCLSTokenizer` for automatic CLS token addition
2. Use model's `cls_token_ids` (60000-60015) for multi-CLS tokens
3. Route embeddings correctly: regular tokens → `transformer.wte`, CLS tokens → `cls_embeddings`
4. Maintain identical sequence structure as training

**Expected outcome:** Significant performance improvement (likely 0.4+ NDCG@10) and meaningful differences between pooling strategies.
