#!/usr/bin/env python3
"""
Test script to verify that the corrected evaluation pipeline exactly matches the training pipeline.
This script compares tokenization, sequence creation, and embedding lookup between training and evaluation.
"""

import os
import sys
import torch
import yaml

# Add project root to path
sys.path.append(os.path.dirname(__file__))

from models import MaskedLanguageModel
from models.config import load_model_config_from_yaml
from MyTokenizer import AutoCLSTokenizer, is_qwen_tokenizer
from data.dataloader_streaming import DataLoaderStreaming
from eval_encoder_corrected import build_tokenizer_correctly, create_sequence_correctly, get_embeddings_correctly


def test_tokenizer_consistency():
    """Test that tokenizer initialization matches between training and evaluation."""
    print("=== TESTING TOKENIZER CONSISTENCY ===")
    
    tokenizer_path = "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls"
    
    # Training tokenizer (from train.py)
    print("Training tokenizer setup:")
    if is_qwen_tokenizer(tokenizer_path):
        train_tokenizer = AutoCLSTokenizer(tokenizer_path)
    else:
        from transformers import AutoTokenizer
        train_tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
    
    if train_tokenizer.pad_token is None:
        train_tokenizer.pad_token = train_tokenizer.eos_token
    if train_tokenizer.cls_token is None:
        train_tokenizer.add_special_tokens({'cls_token': '[CLS]'})
    if train_tokenizer.mask_token is None:
        train_tokenizer.add_special_tokens({'mask_token': '[MASK]'})
    
    print(f"  Vocab size: {len(train_tokenizer)}")
    print(f"  PAD: {train_tokenizer.pad_token} ({train_tokenizer.pad_token_id})")
    print(f"  CLS: {train_tokenizer.cls_token} ({train_tokenizer.cls_token_id})")
    print(f"  EOS: {train_tokenizer.eos_token} ({train_tokenizer.eos_token_id})")
    print(f"  MASK: {train_tokenizer.mask_token} ({train_tokenizer.mask_token_id})")
    
    # Evaluation tokenizer (from corrected script)
    print("\nEvaluation tokenizer setup:")
    eval_tokenizer = build_tokenizer_correctly(tokenizer_path)
    print(f"  Vocab size: {len(eval_tokenizer)}")
    print(f"  PAD: {eval_tokenizer.pad_token} ({eval_tokenizer.pad_token_id})")
    print(f"  CLS: {eval_tokenizer.cls_token} ({eval_tokenizer.cls_token_id})")
    print(f"  EOS: {eval_tokenizer.eos_token} ({eval_tokenizer.eos_token_id})")
    print(f"  MASK: {eval_tokenizer.mask_token} ({eval_tokenizer.mask_token_id})")
    
    # Compare
    print("\nComparison:")
    print(f"  Vocab sizes match: {len(train_tokenizer) == len(eval_tokenizer)}")
    print(f"  PAD tokens match: {train_tokenizer.pad_token_id == eval_tokenizer.pad_token_id}")
    print(f"  CLS tokens match: {train_tokenizer.cls_token_id == eval_tokenizer.cls_token_id}")
    print(f"  EOS tokens match: {train_tokenizer.eos_token_id == eval_tokenizer.eos_token_id}")
    print(f"  MASK tokens match: {train_tokenizer.mask_token_id == eval_tokenizer.mask_token_id}")
    
    return train_tokenizer, eval_tokenizer


def test_model_initialization():
    """Test that model initialization matches between training and evaluation."""
    print("\n=== TESTING MODEL INITIALIZATION ===")
    
    config_path = "config_multi_cls.yaml"
    tokenizer_path = "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls"
    
    # Training model setup (from train.py)
    print("Training model setup:")
    train_tokenizer = AutoCLSTokenizer(tokenizer_path)
    train_config = load_model_config_from_yaml(config_path)
    train_model = MaskedLanguageModel(train_config, tokenizer=train_tokenizer)
    
    print(f"  use_multi_cls: {train_model.use_multi_cls}")
    print(f"  n_cls_tokens: {train_model.n_cls_tokens}")
    print(f"  cls_token_ids: {train_model.cls_token_ids}")
    print(f"  vocab_size: {train_model.config.vocab_size}")
    print(f"  transformer.wte shape: {train_model.transformer.wte.weight.shape}")
    if hasattr(train_model, 'cls_embeddings'):
        print(f"  cls_embeddings shape: {train_model.cls_embeddings.shape}")
    
    # Evaluation model setup (from corrected script)
    print("\nEvaluation model setup:")
    eval_tokenizer = build_tokenizer_correctly(tokenizer_path)
    eval_config = load_model_config_from_yaml(config_path)
    eval_model = MaskedLanguageModel(eval_config, tokenizer=eval_tokenizer)
    
    print(f"  use_multi_cls: {eval_model.use_multi_cls}")
    print(f"  n_cls_tokens: {eval_model.n_cls_tokens}")
    print(f"  cls_token_ids: {eval_model.cls_token_ids}")
    print(f"  vocab_size: {eval_model.config.vocab_size}")
    print(f"  transformer.wte shape: {eval_model.transformer.wte.weight.shape}")
    if hasattr(eval_model, 'cls_embeddings'):
        print(f"  cls_embeddings shape: {eval_model.cls_embeddings.shape}")
    
    # Compare
    print("\nComparison:")
    print(f"  use_multi_cls match: {train_model.use_multi_cls == eval_model.use_multi_cls}")
    print(f"  n_cls_tokens match: {train_model.n_cls_tokens == eval_model.n_cls_tokens}")
    print(f"  cls_token_ids match: {train_model.cls_token_ids == eval_model.cls_token_ids}")
    print(f"  vocab_size match: {train_model.config.vocab_size == eval_model.config.vocab_size}")
    print(f"  wte shapes match: {train_model.transformer.wte.weight.shape == eval_model.transformer.wte.weight.shape}")
    
    return train_model, eval_model, train_tokenizer, eval_tokenizer


def test_sequence_creation():
    """Test that sequence creation matches between training and evaluation."""
    print("\n=== TESTING SEQUENCE CREATION ===")
    
    # Setup
    train_model, eval_model, train_tokenizer, eval_tokenizer = test_model_initialization()
    device = torch.device("cpu")
    max_len = 512
    test_text = "This is a test document for sequence creation comparison."
    
    print(f"Test text: '{test_text}'")
    
    # Training sequence creation (simulating DataLoaderStreaming)
    print("\nTraining sequence creation:")
    
    # Step 1: Tokenize with AutoCLSTokenizer (adds [cls] automatically)
    train_encoded = train_tokenizer.encode(test_text, add_special_tokens=True, truncation=True, max_length=max_len-17)
    print(f"  After tokenization: {train_encoded[:10]}... (length: {len(train_encoded)})")
    
    # Step 2: Extract content tokens (remove the single CLS)
    if train_encoded[0] == train_tokenizer.cls_token_id:
        train_content = train_encoded[1:]
    else:
        train_content = train_encoded
    print(f"  Content tokens: {train_content[:10]}... (length: {len(train_content)})")
    
    # Step 3: Create sequence like DataLoaderStreaming
    if train_model.use_multi_cls:
        content_length = max_len - train_model.n_cls_tokens - 1
        train_content = train_content[:content_length]
        train_sequence = train_model.cls_token_ids[:train_model.n_cls_tokens] + train_content + [train_tokenizer.eos_token_id]
    else:
        content_length = max_len - 2
        train_content = train_content[:content_length]
        train_sequence = [train_tokenizer.cls_token_id] + train_content + [train_tokenizer.eos_token_id]
    
    # Pad
    while len(train_sequence) < max_len:
        train_sequence.append(train_tokenizer.pad_token_id)
    train_sequence = train_sequence[:max_len]
    
    print(f"  Final sequence: {train_sequence[:20]}... (length: {len(train_sequence)})")
    
    # Evaluation sequence creation (using corrected function)
    print("\nEvaluation sequence creation:")
    eval_sequence = create_sequence_correctly(eval_tokenizer, test_text, eval_model, max_len, device)
    print(f"  Final sequence: {eval_sequence[:20].tolist()}... (length: {len(eval_sequence)})")
    
    # Compare
    print("\nComparison:")
    sequences_match = train_sequence == eval_sequence.tolist()
    print(f"  Sequences match: {sequences_match}")
    
    if not sequences_match:
        print("  Differences found:")
        for i, (t, e) in enumerate(zip(train_sequence, eval_sequence.tolist())):
            if t != e:
                print(f"    Position {i}: training={t}, evaluation={e}")
                if i > 10:  # Limit output
                    break
    
    return train_sequence, eval_sequence.tolist()


def test_embedding_lookup():
    """Test that embedding lookup matches between training and evaluation."""
    print("\n=== TESTING EMBEDDING LOOKUP ===")
    
    # Setup
    train_model, eval_model, train_tokenizer, eval_tokenizer = test_model_initialization()
    device = torch.device("cpu")
    
    # Create test sequence
    test_sequence = [60000, 60001, 60002, 60003, 60004, 60005, 60006, 60007,
                    60008, 60009, 60010, 60011, 60012, 60013, 60014, 60015,
                    1234, 5678, 9012, eval_tokenizer.eos_token_id, eval_tokenizer.pad_token_id]
    
    input_ids = torch.tensor([test_sequence], dtype=torch.long, device=device)
    attention_mask = (input_ids != eval_tokenizer.pad_token_id).long()
    
    print(f"Test sequence: {test_sequence}")
    print(f"Input shape: {input_ids.shape}")
    
    # Training embedding lookup (simulating model forward pass)
    print("\nTraining embedding lookup:")
    train_model.eval()
    
    B, T = input_ids.size()
    train_x = torch.zeros(B, T, train_model.config.n_embd, device=device, dtype=train_model.transformer.wte.weight.dtype)
    
    # Regular tokens
    regular_mask = torch.ones_like(input_ids, dtype=torch.bool)
    for cls_id in train_model.cls_token_ids:
        regular_mask = regular_mask & (input_ids != cls_id)
    
    if regular_mask.any():
        regular_tokens = input_ids[regular_mask]
        vocab_size = train_model.transformer.wte.num_embeddings
        regular_tokens = torch.clamp(regular_tokens, 0, vocab_size - 1)
        train_x[regular_mask] = train_model.transformer.wte(regular_tokens)
    
    # CLS tokens
    for i, cls_id in enumerate(train_model.cls_token_ids):
        cls_mask = (input_ids == cls_id)
        if cls_mask.any():
            train_x[cls_mask] = train_model.cls_embeddings[i]
    
    print(f"  Embedding shape: {train_x.shape}")
    print(f"  First few embeddings norm: {torch.norm(train_x[0, :5], dim=-1)}")
    
    # Evaluation embedding lookup (using corrected function)
    print("\nEvaluation embedding lookup:")
    eval_model.eval()
    
    # Copy weights to ensure same embeddings
    eval_model.load_state_dict(train_model.state_dict())
    
    # Get embeddings using corrected function (just the embedding part)
    B, T = input_ids.size()
    eval_x = torch.zeros(B, T, eval_model.config.n_embd, device=device, dtype=eval_model.transformer.wte.weight.dtype)
    
    # Regular tokens
    regular_mask = torch.ones_like(input_ids, dtype=torch.bool)
    for cls_id in eval_model.cls_token_ids:
        regular_mask = regular_mask & (input_ids != cls_id)
    
    if regular_mask.any():
        regular_tokens = input_ids[regular_mask]
        vocab_size = eval_model.transformer.wte.num_embeddings
        regular_tokens = torch.clamp(regular_tokens, 0, vocab_size - 1)
        eval_x[regular_mask] = eval_model.transformer.wte(regular_tokens)
    
    # CLS tokens
    for i, cls_id in enumerate(eval_model.cls_token_ids):
        cls_mask = (input_ids == cls_id)
        if cls_mask.any():
            eval_x[cls_mask] = eval_model.cls_embeddings[i]
    
    print(f"  Embedding shape: {eval_x.shape}")
    print(f"  First few embeddings norm: {torch.norm(eval_x[0, :5], dim=-1)}")
    
    # Compare
    print("\nComparison:")
    embeddings_match = torch.allclose(train_x, eval_x, atol=1e-6)
    print(f"  Embeddings match: {embeddings_match}")
    
    if not embeddings_match:
        diff = torch.abs(train_x - eval_x)
        max_diff = torch.max(diff)
        print(f"  Max difference: {max_diff}")
        print(f"  Mean difference: {torch.mean(diff)}")


def main():
    """Run all consistency tests."""
    print("Testing pipeline consistency between training and corrected evaluation...\n")
    
    try:
        # Test 1: Tokenizer consistency
        test_tokenizer_consistency()
        
        # Test 2: Model initialization consistency  
        test_model_initialization()
        
        # Test 3: Sequence creation consistency
        test_sequence_creation()
        
        # Test 4: Embedding lookup consistency
        test_embedding_lookup()
        
        print("\n=== SUMMARY ===")
        print("All tests completed. Check output above for any inconsistencies.")
        print("If all tests pass, the corrected evaluation should match training exactly.")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
