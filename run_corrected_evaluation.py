#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the corrected evaluation and compare with previous results.
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def run_evaluation(dataset="scifact", pooling="cls_avg", batch_size=16):
    """Run the corrected evaluation script."""
    
    checkpoint_path = "log_encoder_mlm_fineweb_10B_pure_multi_cls/model_19999.pt"
    config_path = "config_multi_cls.yaml"
    tokenizer_path = "/s2_nfs/tokenizer_spm_50368_140B_EnFa_hf_edited-cls"
    
    # Check if checkpoint exists
    if not os.path.exists(checkpoint_path):
        print(f"Error: Checkpoint not found at {checkpoint_path}")
        return False
    
    # Check if config exists
    if not os.path.exists(config_path):
        print(f"Error: Config not found at {config_path}")
        return False
    
    print(f"=== Running Corrected Evaluation ===")
    print(f"Dataset: {dataset}")
    print(f"Pooling: {pooling}")
    print(f"Batch size: {batch_size}")
    print(f"Checkpoint: {checkpoint_path}")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Build command
    cmd = [
        "python3", "eval_encoder_corrected.py",
        "--checkpoint", checkpoint_path,
        "--config", config_path,
        "--tokenizer_path", tokenizer_path,
        "--dataset", dataset,
        "--pooling", pooling,
        "--batch_size", str(batch_size),
        "--max_len", "1024",
        "--device", "cuda" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "cpu"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    print()
    
    # Run evaluation
    start_time = time.time()
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1 hour timeout
        end_time = time.time()
        
        print("=== STDOUT ===")
        print(result.stdout)
        
        if result.stderr:
            print("=== STDERR ===")
            print(result.stderr)
        
        print(f"\n=== Execution Summary ===")
        print(f"Return code: {result.returncode}")
        print(f"Execution time: {end_time - start_time:.2f} seconds")
        
        if result.returncode == 0:
            print("✅ Evaluation completed successfully!")
            
            # Extract NDCG@10 from output
            lines = result.stdout.split('\n')
            for line in lines:
                if 'NDCG@10:' in line:
                    ndcg_10 = line.split('NDCG@10:')[1].strip()
                    print(f"🎯 Primary metric - NDCG@10: {ndcg_10}")
                    break
            
            return True
        else:
            print("❌ Evaluation failed!")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Evaluation timed out after 1 hour!")
        return False
    except Exception as e:
        print(f"❌ Error running evaluation: {e}")
        return False


def compare_with_previous_results():
    """Compare with previous evaluation results."""
    print("\n=== Comparison with Previous Results ===")
    
    # Look for previous results
    previous_results_file = "eval/eval_multi_cls_results.md"
    if os.path.exists(previous_results_file):
        print(f"Previous results found at: {previous_results_file}")
        
        # Read previous results
        with open(previous_results_file, 'r') as f:
            content = f.read()
        
        # Extract NDCG@10 values from previous results
        lines = content.split('\n')
        previous_ndcg = {}
        
        for line in lines:
            if 'NDCG@10' in line and '|' in line:
                parts = line.split('|')
                if len(parts) >= 3:
                    pooling = parts[1].strip()
                    ndcg = parts[2].strip()
                    if pooling and ndcg and pooling != 'Pooling Method':
                        try:
                            ndcg_val = float(ndcg)
                            previous_ndcg[pooling] = ndcg_val
                        except ValueError:
                            pass
        
        if previous_ndcg:
            print("\nPrevious NDCG@10 results:")
            for pooling, ndcg in previous_ndcg.items():
                print(f"  {pooling}: {ndcg:.4f}")
            
            print(f"\nExpected improvement: Previous results were ~0.23, corrected should be >0.4")
        else:
            print("Could not parse previous NDCG@10 values")
    else:
        print("No previous results file found")


def run_pipeline_test():
    """Run the pipeline consistency test first."""
    print("=== Running Pipeline Consistency Test ===")
    
    try:
        result = subprocess.run(["python3", "test_pipeline_consistency.py"], 
                              capture_output=True, text=True, timeout=300)
        
        print("Test output:")
        print(result.stdout)
        
        if result.stderr:
            print("Test errors:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ Pipeline consistency test passed!")
            return True
        else:
            print("❌ Pipeline consistency test failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error running pipeline test: {e}")
        return False


def main():
    """Main execution function."""
    print("Corrected Multi-CLS Evaluation Runner")
    print("=" * 50)
    
    # Step 1: Run pipeline consistency test
    if not run_pipeline_test():
        print("\n❌ Pipeline test failed. Please fix issues before running evaluation.")
        return
    
    print("\n" + "=" * 50)
    
    # Step 2: Compare with previous results
    compare_with_previous_results()
    
    print("\n" + "=" * 50)
    
    # Step 3: Run corrected evaluation
    datasets_to_test = ["scifact"]  # Start with one dataset
    pooling_methods = ["cls_avg"]   # Start with one pooling method
    
    success_count = 0
    total_count = 0
    
    for dataset in datasets_to_test:
        for pooling in pooling_methods:
            total_count += 1
            print(f"\n{'='*20} Test {total_count} {'='*20}")
            
            success = run_evaluation(dataset=dataset, pooling=pooling, batch_size=16)
            if success:
                success_count += 1
            
            print(f"{'='*50}")
    
    # Summary
    print(f"\n=== Final Summary ===")
    print(f"Successful evaluations: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All evaluations completed successfully!")
        print("\nNext steps:")
        print("1. Check the results files in eval/ directory")
        print("2. Compare NDCG@10 values with previous results")
        print("3. If results are improved, run on more datasets and pooling methods")
        print("4. Run: python3 eval_encoder_corrected.py --dataset scifact --pooling cls_weighted")
        print("5. Run: python3 eval_encoder_corrected.py --dataset scifact --pooling cls_concat")
    else:
        print("❌ Some evaluations failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
